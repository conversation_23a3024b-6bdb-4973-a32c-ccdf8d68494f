#!/usr/bin/env python3
"""
Test script for download control functionality.
This script tests the stop, pause, resume, and cancel buttons for downloads.
"""

import sys
import time
import threading
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.server import FileTransferServer
from src.core.resilient_server import ResilientFileTransferServer
from src.core.client import FileTransferClient
from src.core.resilient_client import ResilientFileTransferClient
from src.utils.network_utils import NetworkUtils


def create_test_file(filename: str, size_mb: int) -> Path:
    """Create a test file of specified size."""
    test_file = Path(filename)
    
    # Create file with random data
    with open(test_file, 'wb') as f:
        chunk_size = 1024 * 1024  # 1MB chunks
        for _ in range(size_mb):
            f.write(b'A' * chunk_size)
    
    print(f"Created test file: {test_file} ({size_mb}MB)")
    return test_file


def test_standard_server_controls():
    """Test download controls with standard server."""
    print("\n=== Testing Standard Server Download Controls ===")
    
    # Setup
    port = NetworkUtils.find_available_port(start_port=9000)
    download_dir = Path("test_downloads_standard")
    download_dir.mkdir(exist_ok=True)
    
    # Create test file
    test_file = create_test_file("test_standard.dat", 10)  # 10MB file
    
    try:
        # Start server
        server = FileTransferServer(
            host="localhost",
            port=port,
            download_dir=str(download_dir),
            chunk_size=64*1024  # 64KB chunks for slower transfer
        )
        
        # Track server events
        transfer_events = []
        
        def on_transfer_progress(filename, progress, bytes_received, total_bytes, client_id):
            transfer_events.append({
                'event': 'progress',
                'filename': filename,
                'progress': progress,
                'bytes_received': bytes_received,
                'total_bytes': total_bytes,
                'client_id': client_id
            })
            print(f"Progress: {filename} - {progress:.1f}% ({bytes_received}/{total_bytes} bytes)")
        
        def on_file_received(filename, client_id):
            transfer_events.append({
                'event': 'completed',
                'filename': filename,
                'client_id': client_id
            })
            print(f"File received: {filename}")
        
        server.on_transfer_progress = on_transfer_progress
        server.on_file_received = on_file_received
        
        # Start server in thread
        server_thread = threading.Thread(target=server.start, daemon=True)
        server_thread.start()
        time.sleep(0.5)  # Let server start
        
        print(f"Server started on localhost:{port}")
        
        # Test download controls
        print("\n1. Testing pause functionality...")
        
        # Start client transfer
        client = FileTransferClient()
        if client.connect("localhost", port):
            print("Client connected")
            
            # Start transfer in thread
            def send_file():
                client.send_file(str(test_file))
            
            transfer_thread = threading.Thread(target=send_file, daemon=True)
            transfer_thread.start()
            
            # Let transfer start
            time.sleep(1)
            
            # Test pause
            print("Pausing downloads...")
            server.pause_downloads()
            time.sleep(2)
            
            # Test resume
            print("Resuming downloads...")
            server.resume_downloads()
            time.sleep(2)
            
            # Test stop
            print("Stopping downloads...")
            server.stop_downloads()
            time.sleep(1)
            
            client.disconnect()
        
        # Stop server
        server.stop()
        print("Standard server test completed")
        
        # Analyze results
        print(f"\nTransfer events recorded: {len(transfer_events)}")
        if transfer_events:
            max_progress = max(event['progress'] for event in transfer_events if event['event'] == 'progress')
            print(f"Maximum progress reached: {max_progress:.1f}%")
        
    except Exception as e:
        print(f"Error in standard server test: {e}")
    finally:
        # Cleanup
        if test_file.exists():
            test_file.unlink()
        if download_dir.exists():
            for file in download_dir.glob("*"):
                file.unlink()
            download_dir.rmdir()


def test_resilient_server_controls():
    """Test download controls with resilient server."""
    print("\n=== Testing Resilient Server Download Controls ===")
    
    # Setup
    port = NetworkUtils.find_available_port(start_port=9100)
    download_dir = Path("test_downloads_resilient")
    download_dir.mkdir(exist_ok=True)
    
    # Create test file
    test_file = create_test_file("test_resilient.dat", 5)  # 5MB file
    
    try:
        # Start resilient server
        server = ResilientFileTransferServer(
            host="localhost",
            port=port,
            download_dir=str(download_dir),
            chunk_size=32*1024  # 32KB chunks
        )
        
        # Track server events
        transfer_events = []
        
        def on_transfer_progress(filename, progress, bytes_received, total_bytes, client_id):
            transfer_events.append({
                'event': 'progress',
                'filename': filename,
                'progress': progress,
                'bytes_received': bytes_received,
                'total_bytes': total_bytes,
                'client_id': client_id
            })
            print(f"Progress: {filename} - {progress:.1f}% ({bytes_received}/{total_bytes} bytes)")
        
        def on_file_received(filename, client_id):
            transfer_events.append({
                'event': 'completed',
                'filename': filename,
                'client_id': client_id
            })
            print(f"File received: {filename}")
        
        server.on_transfer_progress = on_transfer_progress
        server.on_file_received = on_file_received
        
        # Start server in thread
        server_thread = threading.Thread(target=server.start, daemon=True)
        server_thread.start()
        time.sleep(0.5)  # Let server start
        
        print(f"Resilient server started on localhost:{port}")
        
        # Test download controls
        print("\n1. Testing resilient server controls...")
        
        # Start resilient client transfer
        client = ResilientFileTransferClient()
        if client.connect("localhost", port):
            print("Resilient client connected")
            
            # Start transfer in thread
            def send_file():
                client.send_file(str(test_file), resume=True)
            
            transfer_thread = threading.Thread(target=send_file, daemon=True)
            transfer_thread.start()
            
            # Let transfer start
            time.sleep(1)
            
            # Test pause
            print("Pausing downloads...")
            server.pause_downloads()
            time.sleep(2)
            
            # Test resume
            print("Resuming downloads...")
            server.resume_downloads()
            time.sleep(2)
            
            # Test cancel
            print("Cancelling downloads...")
            server.cancel_downloads()
            time.sleep(1)
            
            client.disconnect()
        
        # Stop server
        server.stop()
        print("Resilient server test completed")
        
        # Analyze results
        print(f"\nTransfer events recorded: {len(transfer_events)}")
        if transfer_events:
            max_progress = max(event['progress'] for event in transfer_events if event['event'] == 'progress')
            print(f"Maximum progress reached: {max_progress:.1f}%")
        
    except Exception as e:
        print(f"Error in resilient server test: {e}")
    finally:
        # Cleanup
        if test_file.exists():
            test_file.unlink()
        if download_dir.exists():
            for file in download_dir.glob("*"):
                file.unlink()
            download_dir.rmdir()


def main():
    """Run all download control tests."""
    print("=== Download Control Functionality Tests ===")
    print("This script tests the stop, pause, resume, and cancel functionality")
    print("for both standard and resilient file transfer servers.")
    
    try:
        # Test standard server
        test_standard_server_controls()
        
        # Test resilient server
        test_resilient_server_controls()
        
        print("\n=== All Tests Completed ===")
        print("✅ Download control functionality has been tested")
        print("Check the output above for any issues or errors")
        
    except KeyboardInterrupt:
        print("\nTests interrupted by user")
    except Exception as e:
        print(f"\nUnexpected error during testing: {e}")


if __name__ == "__main__":
    main()
