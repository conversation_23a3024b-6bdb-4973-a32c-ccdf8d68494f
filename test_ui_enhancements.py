#!/usr/bin/env python3
"""
Test script to demonstrate the enhanced UI features:
- Scrollable views
- Modern color scheme
- Section-specific backgrounds
- Professional styling
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from gui.theme import ModernTheme


class UITestWindow:
    """Test window to demonstrate UI enhancements."""
    
    def __init__(self):
        """Initialize the test window."""
        self.root = tk.Tk()
        self.root.title("File Transfer UI Enhancements Test")
        self.root.geometry("900x600")
        self.root.minsize(700, 500)
        
        # Apply modern theme
        self.style = ModernTheme.apply_theme(self.root)
        
        # Create widgets
        self._create_widgets()
        
        # Center window
        self._center_window()
    
    def _center_window(self):
        """Center the window on screen."""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def _create_widgets(self):
        """Create and layout widgets."""
        # Main container
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Header section with new styling
        header_frame = ttk.Frame(main_frame, style='Header.TFrame', padding="20")
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        title_label = ttk.Label(header_frame, text="File Transfer Pro - UI Enhancements",
                               style='Title.TLabel', font=('Segoe UI', 18, 'bold'))
        title_label.pack()
        
        subtitle_label = ttk.Label(header_frame, text="Modern, Professional Interface with Enhanced Usability",
                                  style='Subtitle.TLabel', font=('Segoe UI', 10))
        subtitle_label.pack(pady=(5, 0))
        
        # Create notebook for tabs
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # Create test tabs
        self._create_scrollable_demo_tab(notebook)
        self._create_color_scheme_demo_tab(notebook)
        self._create_button_demo_tab(notebook)
        
        # Status bar
        status_frame = ttk.Frame(main_frame, style='Status.TFrame', padding="10")
        status_frame.pack(fill=tk.X, pady=(10, 0))
        
        status_label = ttk.Label(status_frame, text="✅ UI Enhancements Active", style='Status.TLabel')
        status_label.pack(side=tk.LEFT)
        
        info_label = ttk.Label(status_frame, text="Scrollable • Modern Colors • Professional Design", style='Status.TLabel')
        info_label.pack(side=tk.RIGHT)
    
    def _create_scrollable_demo_tab(self, notebook):
        """Create a tab demonstrating scrollable functionality."""
        # Create main tab frame
        tab_frame = ttk.Frame(notebook)
        notebook.add(tab_frame, text="📜 Scrollable Demo")
        
        # Create scrollable frame using the theme helper
        canvas, scrollbar, scrollable_frame = ModernTheme.create_scrollable_frame(tab_frame, 'Transfer.TFrame')
        
        # Pack the canvas and scrollbar
        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(20, 5), pady=20)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=20)
        
        # Add content to demonstrate scrolling
        for i in range(20):
            section_frame = ttk.LabelFrame(scrollable_frame, 
                                         text=f"📁 Section {i+1}", 
                                         padding="15", 
                                         style='ContentSection.TLabelframe')
            section_frame.pack(fill=tk.X, pady=(0, 15))
            
            # Add some content
            ttk.Label(section_frame, text=f"This is content for section {i+1}. " * 3).pack(anchor=tk.W)
            
            # Add some buttons
            btn_frame = ttk.Frame(section_frame)
            btn_frame.pack(fill=tk.X, pady=(10, 0))
            
            ttk.Button(btn_frame, text="Action", style='PrimaryAction.TButton', width=12).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(btn_frame, text="Secondary", style='Secondary.TButton', width=12).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(btn_frame, text="Success", style='SuccessAction.TButton', width=12).pack(side=tk.LEFT)
    
    def _create_color_scheme_demo_tab(self, notebook):
        """Create a tab demonstrating the color scheme."""
        # Create main tab frame
        tab_frame = ttk.Frame(notebook)
        notebook.add(tab_frame, text="🎨 Color Scheme")
        
        # Create scrollable frame
        canvas, scrollbar, scrollable_frame = ModernTheme.create_scrollable_frame(tab_frame, 'Settings.TFrame')
        
        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(20, 5), pady=20)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=20)
        
        # Section backgrounds demo
        sections = [
            ("Header Section", "Header.TFrame"),
            ("Transfer Section", "Transfer.TFrame"),
            ("Server Section", "Server.TFrame"),
            ("Settings Section", "Settings.TFrame"),
            ("Status Section", "Status.TFrame"),
        ]
        
        for name, style in sections:
            demo_frame = ttk.Frame(scrollable_frame, style=style, padding="20")
            demo_frame.pack(fill=tk.X, pady=(0, 15))
            
            ttk.Label(demo_frame, text=f"{name} Background", 
                     font=('Segoe UI', 12, 'bold')).pack(anchor=tk.W)
            ttk.Label(demo_frame, text=f"This demonstrates the {name.lower()} with its specific background color.").pack(anchor=tk.W, pady=(5, 0))
        
        # LabelFrame styles demo
        labelframe_styles = [
            ("Important Section", "Important.TLabelframe"),
            ("Content Section", "ContentSection.TLabelframe"),
            ("Control Panel", "ControlPanel.TLabelframe"),
        ]
        
        for name, style in labelframe_styles:
            demo_frame = ttk.LabelFrame(scrollable_frame, text=f"🔧 {name}", 
                                      padding="15", style=style)
            demo_frame.pack(fill=tk.X, pady=(0, 15))
            
            ttk.Label(demo_frame, text=f"This is a {name.lower()} with enhanced styling.").pack(anchor=tk.W)
    
    def _create_button_demo_tab(self, notebook):
        """Create a tab demonstrating button styles."""
        tab_frame = ttk.Frame(notebook)
        notebook.add(tab_frame, text="🔘 Button Styles")
        
        # Create scrollable frame
        canvas, scrollbar, scrollable_frame = ModernTheme.create_scrollable_frame(tab_frame, 'TFrame')
        
        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(20, 5), pady=20)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=20)
        
        # Button styles demo
        button_styles = [
            ("Primary Action", "PrimaryAction.TButton"),
            ("Success Action", "SuccessAction.TButton"),
            ("Danger Action", "DangerAction.TButton"),
            ("Connect Action", "ConnectAction.TButton"),
            ("Secondary", "Secondary.TButton"),
            ("Default", "TButton"),
        ]
        
        for name, style in button_styles:
            section_frame = ttk.LabelFrame(scrollable_frame, text=f"🎯 {name} Buttons", 
                                         padding="15", style='ContentSection.TLabelframe')
            section_frame.pack(fill=tk.X, pady=(0, 15))
            
            btn_frame = ttk.Frame(section_frame)
            btn_frame.pack(fill=tk.X)
            
            ttk.Button(btn_frame, text=f"{name}", style=style, width=15).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(btn_frame, text=f"{name} Disabled", style=style, width=15, state='disabled').pack(side=tk.LEFT)
            
            ttk.Label(section_frame, text=f"Style: {style}").pack(anchor=tk.W, pady=(10, 0))
    
    def run(self):
        """Run the test application."""
        self.root.mainloop()


if __name__ == "__main__":
    app = UITestWindow()
    app.run()
