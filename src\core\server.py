"""
TCP Server implementation for file transfer.
"""

import socket
import threading
import json
import os
import time
from pathlib import Path
from typing import Optional, Callable, Dict, Any
from src.utils.logger import get_logger
from src.utils.compression import FileCompressor, CompressionMethod
from src.utils.file_utils import FileUtils
from src.core.protocol import FileMetadata


class FileTransferServer:
    """
    TCP server for receiving files from clients.
    """
    
    def __init__(
        self,
        host: str = "localhost",
        port: int = 8888,
        download_dir: Optional[str] = None,
        chunk_size: int = 65536,  # Increased from 8192 to 64KB
        max_connections: int = 5,
        enable_compression: bool = True
    ):
        """
        Initialize the file transfer server.

        Args:
            host: Server host address
            port: Server port number
            download_dir: Directory to save received files
            chunk_size: Size of data chunks for transfer
            max_connections: Maximum concurrent connections
            enable_compression: Whether to support file decompression
        """
        self.host = host
        self.port = port
        self.chunk_size = chunk_size
        self.max_connections = max_connections
        self.enable_compression = enable_compression
        self.logger = get_logger()

        # Initialize decompressor
        if self.enable_compression:
            self.decompressor = FileCompressor()
        else:
            self.decompressor = None
        
        # Set default download directory
        if download_dir is None:
            self.download_dir = Path.home() / "Downloads" / "FileTransfer"
        else:
            self.download_dir = Path(download_dir)
        
        # Create download directory if it doesn't exist
        self.download_dir.mkdir(parents=True, exist_ok=True)
        
        # Server state
        self.socket = None
        self.running = False
        self.active_connections = {}

        # Download control state
        self.downloads_paused = False
        self.downloads_stopped = False
        
        # Callbacks for GUI integration
        self.on_client_connected: Optional[Callable] = None
        self.on_client_disconnected: Optional[Callable] = None
        self.on_file_received: Optional[Callable] = None
        self.on_transfer_progress: Optional[Callable] = None
        
    def start(self):
        """Start the server and listen for connections."""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)

            # Optimize socket for better performance
            try:
                self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 1024 * 1024)  # 1MB send buffer
                self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 1024 * 1024)  # 1MB receive buffer
            except OSError:
                # Some systems may not allow setting large buffer sizes
                pass

            self.socket.bind((self.host, self.port))
            self.socket.listen(self.max_connections)
            
            self.running = True
            self.logger.info(f"Server started on {self.host}:{self.port}")
            self.logger.info(f"Download directory: {self.download_dir}")
            
            while self.running:
                try:
                    client_socket, client_address = self.socket.accept()

                    # Optimize client socket for better performance
                    try:
                        client_socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)  # Disable Nagle's algorithm
                        client_socket.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, 1024 * 1024)  # 1MB send buffer
                        client_socket.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, 1024 * 1024)  # 1MB receive buffer
                    except OSError:
                        # Some systems may not allow setting these options
                        pass

                    self.logger.info(f"New connection from {client_address}")

                    # Handle client in separate thread
                    client_thread = threading.Thread(
                        target=self._handle_client,
                        args=(client_socket, client_address),
                        daemon=True
                    )
                    client_thread.start()
                    
                except socket.error as e:
                    if self.running:
                        self.logger.error(f"Socket error: {e}")
                    break
                    
        except Exception as e:
            self.logger.error(f"Server error: {e}")
            raise
        finally:
            self.stop()
    
    def stop(self):
        """Stop the server and close all connections."""
        self.running = False

        # Close all active connections (create a copy to avoid iteration issues)
        connections_copy = dict(self.active_connections)
        for client_id, client_socket in connections_copy.items():
            try:
                client_socket.close()
            except:
                pass

        self.active_connections.clear()

        # Close server socket
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None

        self.logger.info("Server stopped")

    def stop_downloads(self):
        """Stop all active downloads."""
        self.downloads_stopped = True
        self.downloads_paused = False
        self.logger.info("All downloads stopped")

    def pause_downloads(self):
        """Pause all active downloads."""
        self.downloads_paused = True
        self.downloads_stopped = False
        self.logger.info("All downloads paused")

    def resume_downloads(self):
        """Resume all paused downloads."""
        self.downloads_paused = False
        self.downloads_stopped = False
        self.logger.info("All downloads resumed")

    def cancel_downloads(self):
        """Cancel all active downloads."""
        self.downloads_stopped = True
        self.downloads_paused = False

        # Close all active connections
        connections_copy = dict(self.active_connections)
        for client_id, client_socket in connections_copy.items():
            try:
                client_socket.close()
            except:
                pass

        self.active_connections.clear()
        self.logger.info("All downloads cancelled")

    def _handle_client(self, client_socket: socket.socket, client_address: tuple):
        """
        Handle individual client connection.
        
        Args:
            client_socket: Client socket connection
            client_address: Client address tuple (host, port)
        """
        client_id = f"{client_address[0]}:{client_address[1]}"
        self.active_connections[client_id] = client_socket
        
        try:
            if self.on_client_connected:
                self.on_client_connected(client_id, client_address)
            
            while self.running:
                # Receive file metadata
                metadata = self._receive_metadata(client_socket)
                if not metadata:
                    break
                
                # Receive file content
                success = self._receive_file(client_socket, metadata, client_id)
                
                # Send response to client
                response = {"status": "success" if success else "error"}
                self._send_response(client_socket, response)
                
                if success and self.on_file_received:
                    self.on_file_received(metadata["filename"], client_id)
                
        except Exception as e:
            self.logger.error(f"Error handling client {client_id}: {e}")
        finally:
            # Clean up connection
            try:
                client_socket.close()
            except:
                pass
            
            if client_id in self.active_connections:
                del self.active_connections[client_id]
            
            if self.on_client_disconnected:
                self.on_client_disconnected(client_id)
            
            self.logger.info(f"Client {client_id} disconnected")
    
    def _receive_metadata(self, client_socket: socket.socket) -> Optional[Dict[str, Any]]:
        """
        Receive file metadata from client.
        
        Args:
            client_socket: Client socket
            
        Returns:
            File metadata dictionary or None if failed
        """
        try:
            # Receive metadata length (4 bytes)
            length_data = client_socket.recv(4)
            if len(length_data) != 4:
                return None
            
            metadata_length = int.from_bytes(length_data, byteorder='big')
            
            # Receive metadata JSON
            metadata_data = b""
            while len(metadata_data) < metadata_length:
                chunk = client_socket.recv(metadata_length - len(metadata_data))
                if not chunk:
                    return None
                metadata_data += chunk
            
            metadata = json.loads(metadata_data.decode('utf-8'))
            self.logger.info(f"Received metadata: {metadata}")
            return metadata
            
        except Exception as e:
            self.logger.error(f"Error receiving metadata: {e}")
            return None

    def _receive_file(self, client_socket: socket.socket, metadata: Dict[str, Any], client_id: str) -> bool:
        """
        Receive file content from client with decompression support.

        Args:
            client_socket: Client socket
            metadata: File metadata (may include compression info)
            client_id: Client identifier

        Returns:
            True if file received successfully, False otherwise
        """
        try:
            # Parse metadata (support both old and new format)
            if isinstance(metadata, dict) and "filename" in metadata:
                # Old format - convert to FileMetadata
                file_metadata = FileMetadata.from_dict(metadata)
            else:
                file_metadata = metadata

            filename = file_metadata.filename
            file_size = file_metadata.size
            is_compressed = getattr(file_metadata, 'is_compressed', False)
            compression_method = getattr(file_metadata, 'compression_method', 'none')
            original_size = getattr(file_metadata, 'original_size', file_size)

            # Create safe filename
            safe_filename = self._get_safe_filename(filename)

            # If file is compressed, receive to temporary file first
            if is_compressed and self.enable_compression and self.decompressor:
                temp_file_path = self.download_dir / f".tmp_{safe_filename}_{compression_method}"
                final_file_path = self.download_dir / safe_filename
            else:
                temp_file_path = self.download_dir / safe_filename
                final_file_path = temp_file_path

            self.logger.info(f"Receiving file: {filename} ({FileUtils.format_file_size(file_size)})")
            if is_compressed:
                self.logger.info(f"File is compressed with {compression_method}, original size: {FileUtils.format_file_size(original_size)}")

            received_bytes = 0

            # Receive the file data
            with open(temp_file_path, 'wb') as f:
                while received_bytes < file_size:
                    # Check download control state
                    if self.downloads_stopped:
                        self.logger.info(f"Transfer stopped by user: {filename}")
                        return False

                    # Handle pause state
                    while self.downloads_paused:
                        if self.downloads_stopped:
                            return False
                        time.sleep(0.1)  # Wait while paused

                    # Calculate chunk size for this iteration
                    remaining = file_size - received_bytes
                    current_chunk_size = min(self.chunk_size, remaining)

                    # Receive chunk
                    chunk = client_socket.recv(current_chunk_size)
                    if not chunk:
                        self.logger.error("Connection lost during file transfer")
                        return False

                    f.write(chunk)
                    received_bytes += len(chunk)

                    # Report progress (show original file size for compressed files)
                    if self.on_transfer_progress:
                        progress = (received_bytes / file_size) * 100
                        display_size = original_size if is_compressed else file_size
                        display_received = int(progress * display_size / 100)
                        self.on_transfer_progress(filename, progress, display_received, display_size, client_id)

            # Decompress if needed
            if is_compressed and self.enable_compression and self.decompressor:
                self.logger.info(f"Decompressing file: {filename}")

                try:
                    compression_enum = CompressionMethod(compression_method)
                    success = self.decompressor.decompress_file(
                        str(temp_file_path),
                        str(final_file_path),
                        compression_enum
                    )

                    if success:
                        # Remove temporary compressed file
                        os.unlink(temp_file_path)
                        self.logger.info(f"File decompressed successfully: {final_file_path}")
                    else:
                        self.logger.error(f"Failed to decompress file: {filename}")
                        return False

                except Exception as e:
                    self.logger.error(f"Decompression error: {e}")
                    return False

            self.logger.info(f"File received successfully: {final_file_path}")

            # Call the file received callback
            if self.on_file_received:
                self.on_file_received(filename, client_id)

            return True

        except Exception as e:
            self.logger.error(f"Error receiving file: {e}")
            return False

    def _send_response(self, client_socket: socket.socket, response: Dict[str, Any]):
        """
        Send response to client.

        Args:
            client_socket: Client socket
            response: Response dictionary
        """
        try:
            response_data = json.dumps(response).encode('utf-8')
            response_length = len(response_data)

            # Send response length (4 bytes) then response data
            client_socket.send(response_length.to_bytes(4, byteorder='big'))
            client_socket.send(response_data)

        except Exception as e:
            self.logger.error(f"Error sending response: {e}")

    def _get_safe_filename(self, filename: str) -> str:
        """
        Generate a safe filename to prevent path traversal attacks.

        Args:
            filename: Original filename

        Returns:
            Safe filename
        """
        # Remove path components and keep only the filename
        safe_name = os.path.basename(filename)

        # If file already exists, add a number suffix
        base_path = self.download_dir / safe_name
        if base_path.exists():
            name_part = base_path.stem
            extension = base_path.suffix
            counter = 1

            while True:
                new_name = f"{name_part}_{counter}{extension}"
                new_path = self.download_dir / new_name
                if not new_path.exists():
                    return new_name
                counter += 1

        return safe_name

    def get_server_info(self) -> Dict[str, Any]:
        """
        Get server information.

        Returns:
            Dictionary with server information
        """
        return {
            "host": self.host,
            "port": self.port,
            "running": self.running,
            "download_dir": str(self.download_dir),
            "active_connections": len(self.active_connections),
            "max_connections": self.max_connections
        }
